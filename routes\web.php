<?php

use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\ProductCategoryController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductAttributeController;
use App\Http\Controllers\ProductVariantController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\StockTransferController;
use App\Http\Controllers\SupplierController;
use Illuminate\Support\Facades\Route;

// Login Routes
Route::get('/', [AuthController::class, 'showLoginForm'])->name('showloginform');
Route::post('/login', [AuthController::class, 'login'])->name('login');

// Privacy Policy Routes
Route::get('/privacy-policy', function () {
    return view('Rules.privacy-policy');
})->name('privacy-policy');

// Support Routes
Route::get('/support', function () {
    return view('Rules.support');
})->name('support');

Route::group(['middleware' => ['admin.auth']], function () {
    // Page Under Construction Routes
    Route::get('/page-under-construction', function () {
        return view('Layouts.message');
    })->name('page-under-construction');

    // Dashboard and Logout Routes
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/admin-dashboard', [AuthController::class, 'showDashboard'])->name('admin-dashboard');

    // Product Category CRUD (AJAX)
    Route::resource('product-categories', ProductCategoryController::class);

    // Product Management Routes (Resource Controller)
    Route::resource('products', ProductController::class);
    Route::post('products/{product}/duplicate', [ProductController::class, 'duplicate'])->name('products.duplicate');
    Route::delete('products/{product}/gallery/{image}', [ProductController::class, 'removeGalleryImage'])->name('products.remove-gallery-image');
});
