<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Builder;

class DatabaseHelper
{
    /**
     * Cache duration in seconds (1 hour default)
     */
    const DEFAULT_CACHE_DURATION = 3600;

    /**
     * Execute a cached query
     *
     * @param string $cacheKey
     * @param callable $query
     * @param int $duration
     * @return mixed
     */
    public static function cachedQuery(string $cacheKey, callable $query, int $duration = self::DEFAULT_CACHE_DURATION)
    {
        return Cache::remember($cacheKey, $duration, $query);
    }

    /**
     * Execute a query with minimal monitoring (only in development)
     *
     * @param callable $query
     * @param string $queryName
     * @return mixed
     */
    public static function monitoredQuery(callable $query, string $queryName = 'unknown')
    {
        // Only monitor in development environment
        if (app()->environment('local', 'development')) {
            $startTime = microtime(true);

            try {
                $result = $query();

                $executionTime = (microtime(true) - $startTime) * 1000;

                // Log only very slow queries (>2 seconds)
                if ($executionTime > 2000) {
                    Log::warning("Very slow query: {$queryName}", [
                        'execution_time' => $executionTime . 'ms'
                    ]);
                }

                return $result;
            } catch (\Exception $e) {
                Log::error("Query failed: {$queryName}", ['error' => $e->getMessage()]);
                throw $e;
            }
        }

        // In production, just execute without monitoring overhead
        return $query();
    }

    /**
     * Optimize DataTables query for better performance
     *
     * @param Builder $query
     * @param array $columns
     * @param array $searchableColumns
     * @return Builder
     */
    public static function optimizeDataTablesQuery(Builder $query, array $columns = [], array $searchableColumns = [])
    {
        // Only select necessary columns
        if (!empty($columns)) {
            $query->select($columns);
        }

        // Add indexes hint for MySQL
        if (config('database.default') === 'mysql') {
            $table = $query->getModel()->getTable();
            $query->from(DB::raw("{$table} USE INDEX (PRIMARY)"));
        }

        // Optimize search functionality
        if (!empty($searchableColumns) && request()->has('search.value')) {
            $searchValue = request('search.value');
            if (!empty($searchValue)) {
                $query->where(function ($q) use ($searchableColumns, $searchValue) {
                    foreach ($searchableColumns as $column) {
                        $q->orWhere($column, 'LIKE', "%{$searchValue}%");
                    }
                });
            }
        }

        // Optimize ordering
        if (request()->has('order')) {
            $orderColumn = request('order.0.column');
            $orderDir = request('order.0.dir', 'asc');

            if (isset($columns[$orderColumn])) {
                $query->orderBy($columns[$orderColumn], $orderDir);
            }
        }

        return $query;
    }

    /**
     * Batch insert with better performance
     *
     * @param string $table
     * @param array $data
     * @param int $chunkSize
     * @return bool
     */
    public static function batchInsert(string $table, array $data, int $chunkSize = 1000): bool
    {
        if (empty($data)) {
            return true;
        }

        $chunks = array_chunk($data, $chunkSize);

        DB::transaction(function () use ($table, $chunks) {
            foreach ($chunks as $chunk) {
                DB::table($table)->insert($chunk);
            }
        });

        return true;
    }

    /**
     * Optimize table with database-specific commands
     *
     * @param string $table
     * @return bool
     */
    public static function optimizeTable(string $table): bool
    {
        try {
            $driver = config('database.default');
            $connection = config("database.connections.{$driver}");

            switch ($connection['driver']) {
                case 'mysql':
                    DB::statement("OPTIMIZE TABLE {$table}");
                    DB::statement("ANALYZE TABLE {$table}");
                    break;

                case 'pgsql':
                    DB::statement("VACUUM ANALYZE {$table}");
                    break;

                case 'sqlite':
                    DB::statement("VACUUM");
                    break;
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to optimize table {$table}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get basic database performance statistics
     *
     * @return array
     */
    public static function getPerformanceStats(): array
    {
        return [
            'driver' => config('database.default'),
            'cache_enabled' => config('cache.default') !== 'array',
            'cache_store' => config('cache.default'),
            'database_file_exists' => file_exists(database_path('database.sqlite')),
        ];
    }

    /**
     * Clear query cache safely
     *
     * @param string|null $pattern
     * @return bool
     */
    public static function clearQueryCache(?string $pattern = null): bool
    {
        try {
            if ($pattern) {
                // Clear specific cache keys with pattern
                Cache::forget($pattern);
            } else {
                // Clear all cache
                Cache::flush();
            }
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to clear query cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get optimized pagination for large datasets
     *
     * @param Builder $query
     * @param int $perPage
     * @param string $cursorColumn
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public static function optimizedPagination(Builder $query, int $perPage = 15, string $cursorColumn = 'id')
    {
        // Use cursor-based pagination for better performance on large datasets
        if (request()->has('cursor')) {
            $cursor = request('cursor');
            $query->where($cursorColumn, '>', $cursor);
        }

        return $query->orderBy($cursorColumn)->paginate($perPage);
    }
}
