#!/bin/bash

# Production Optimization Script for Arclok Admin
# This script optimizes the Laravel application for production deployment

echo "🚀 Starting production optimization for Arclok Admin..."

# 1. Clear all caches
echo "📦 Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 2. Optimize for production
echo "⚡ Optimizing for production..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 3. Optimize Composer autoloader
echo "🔧 Optimizing Composer autoloader..."
composer install --no-dev --optimize-autoloader

# 4. Set proper permissions
echo "🔐 Setting proper permissions..."
chmod -R 755 storage bootstrap/cache
chmod -R 775 storage/logs
chmod -R 775 storage/framework/cache
chmod -R 775 storage/framework/sessions
chmod -R 775 storage/framework/views

# 5. Create database if it doesn't exist
echo "🗄️ Ensuring database exists..."
touch database/database.sqlite
chmod 664 database/database.sqlite

# 6. Run migrations
echo "📊 Running database migrations..."
php artisan migrate --force

# 7. Clear and optimize caches again
echo "🧹 Final cache optimization..."
php artisan optimize

echo "✅ Production optimization complete!"
echo ""
echo "📋 Next steps:"
echo "1. Set APP_ENV=production in your .env file"
echo "2. Set APP_DEBUG=false in your .env file"
echo "3. Configure your web server (Apache/Nginx)"
echo "4. Set up SSL certificate"
echo "5. Configure backup strategy"
echo ""
echo "🎯 Performance tips:"
echo "- Use Redis for caching in production"
echo "- Enable OPcache in PHP"
echo "- Use a CDN for static assets"
echo "- Monitor with tools like New Relic or Blackfire"
