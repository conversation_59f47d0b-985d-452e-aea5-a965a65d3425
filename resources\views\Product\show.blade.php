@php use App\Helpers\Helper; @endphp
@extends('Layouts.app')
@section('title', 'Product Details - ' . $product->name)

@section('styles')
<style>
    .product-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        color: white;
        margin-bottom: 2rem;
    }
    .product-image-placeholder {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        color: #6c757d;
    }
    .stat-card {
        transition: transform 0.2s ease-in-out;
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    .info-row {
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f3f4;
    }
    .info-row:last-child {
        border-bottom: none;
    }
    .badge-custom {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
    }
    .price-display {
        font-size: 1.5rem;
        font-weight: 700;
        color: #198754;
    }
    .action-btn {
        border-radius: 0.5rem;
        font-weight: 500;
        padding: 0.625rem 1.25rem;
        transition: all 0.2s ease-in-out;
    }
    .action-btn:hover {
        transform: translateY(-1px);
    }
</style>
@endsection

@section('content')
<div class="container-fluid py-4">
    <!-- Enhanced Header -->
    <div class="product-header p-4 mb-4">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-box-open fs-2 me-3"></i>
                    <div>
                        <h1 class="mb-1 text-white">{{ $product->name }}</h1>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-light text-dark me-2">SKU: {{ $product->sku }}</span>
                            @if($product->is_featured)
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-dot mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin-dashboard') }}" class="text-white-50">
                                <i class="fas fa-home me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('products.index') }}" class="text-white-50">Products</a>
                        </li>
                        <li class="breadcrumb-item active text-white">{{ Str::limit($product->name, 30) }}</li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('products.index') }}" class="btn btn-light btn-sm action-btn">
                    <i class="fas fa-arrow-left me-1"></i>Back
                </a>
                <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning btn-sm action-btn">
                    <i class="fas fa-edit me-1"></i>Edit
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stat-card h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-dollar-sign text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Selling Price</div>
                        <div class="price-display">${{ number_format($product->amount, 2) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stat-card h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-boxes text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Stock Quantity</div>
                        <div class="fs-3 fw-bold">{{ number_format($product->stock_quantity) }}</div>
                        @if($product->stock_quantity > 10)
                            <small class="text-success">In Stock</small>
                        @elseif($product->stock_quantity > 0)
                            <small class="text-warning">Low Stock</small>
                        @else
                            <small class="text-danger">Out of Stock</small>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stat-card h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-chart-line text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Profit Margin</div>
                        @php
                            $margin = $product->cost_price ? (($product->amount - $product->cost_price) / $product->amount) * 100 : 0;
                        @endphp
                        <div class="fs-3 fw-bold">{{ number_format($margin, 1) }}%</div>
                        <small class="text-muted">
                            @if($product->cost_price)
                                Cost: ${{ number_format($product->cost_price, 2) }}
                            @else
                                Cost not set
                            @endif
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stat-card h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-calendar text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Created</div>
                        <div class="fs-6 fw-bold">{{ $product->created_at->format('M d, Y') }}</div>
                        <small class="text-muted">{{ $product->created_at->diffForHumans() }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Main Product Information -->
        <div class="col-lg-8">
            <!-- Product Details Card -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        <h5 class="mb-0">Product Details</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Product Name</span>
                                <span class="text-gray-800">{{ $product->name }}</span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">SKU</span>
                                <span class="badge badge-custom bg-primary text-white">{{ $product->sku }}</span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Slug</span>
                                <code class="bg-light px-2 py-1 rounded">{{ $product->slug }}</code>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Category</span>
                                <span>
                                    @if($product->category)
                                        <span class="badge badge-custom bg-success text-white">
                                            <i class="fas fa-tag me-1"></i>{{ $product->category->category }}
                                        </span>
                                    @else
                                        <span class="text-muted">No Category</span>
                                    @endif
                                </span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Status</span>
                                <span>{!! Helper::getStatusBadge($product->status === 'active' ? 1 : 0) !!}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Weight</span>
                                <span>
                                    @if($product->weight)
                                        <i class="fas fa-weight-hanging text-muted me-1"></i>{{ $product->weight }} kg
                                    @else
                                        <span class="text-muted">Not specified</span>
                                    @endif
                                </span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Dimensions</span>
                                <span>
                                    @if($product->length || $product->width || $product->height)
                                        <i class="fas fa-ruler-combined text-muted me-1"></i>
                                        {{ $product->length ?? 0 }}L × {{ $product->width ?? 0 }}W × {{ $product->height ?? 0 }}H
                                    @else
                                        <span class="text-muted">Not specified</span>
                                    @endif
                                </span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Track Inventory</span>
                                <span>
                                    @if($product->track_inventory)
                                        <span class="badge badge-custom bg-success text-white">
                                            <i class="fas fa-check me-1"></i>Enabled
                                        </span>
                                    @else
                                        <span class="badge badge-custom bg-secondary text-white">
                                            <i class="fas fa-times me-1"></i>Disabled
                                        </span>
                                    @endif
                                </span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Featured</span>
                                <span>
                                    @if($product->is_featured)
                                        <span class="badge badge-custom bg-warning text-dark">
                                            <i class="fas fa-star me-1"></i>Yes
                                        </span>
                                    @else
                                        <span class="badge badge-custom bg-light text-dark">No</span>
                                    @endif
                                </span>
                            </div>
                            <div class="info-row d-flex justify-content-between">
                                <span class="fw-semibold text-gray-600">Last Updated</span>
                                <span class="text-muted">{{ $product->updated_at->format('M d, Y H:i') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Description Card -->
            @if($product->short_description || $product->description)
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-align-left text-primary me-2"></i>
                        <h5 class="mb-0">Product Description</h5>
                    </div>
                </div>
                <div class="card-body">
                    @if($product->short_description)
                        <div class="mb-4">
                            <h6 class="text-primary mb-2">
                                <i class="fas fa-quote-left me-1"></i>Short Description
                            </h6>
                            <p class="text-gray-700 mb-0">{{ $product->short_description }}</p>
                        </div>
                    @endif

                    @if($product->description)
                        @if($product->short_description)
                            <hr class="my-4">
                        @endif
                        <div>
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-file-alt me-1"></i>Full Description
                            </h6>
                            <div class="text-gray-700 lh-lg">
                                {!! nl2br(e($product->description)) !!}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Product Image/Media Card -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-images text-primary me-2"></i>
                        <h5 class="mb-0">Product Media</h5>
                    </div>
                </div>
                <div class="card-body text-center">
                    @if($product->featured_image)
                        <img src="{{ $product->featured_image }}" alt="{{ $product->name }}"
                             class="img-fluid rounded mb-3" style="max-height: 200px;">
                    @elseif($product->gallery_images && count($product->gallery_images) > 0)
                        <img src="{{ $product->gallery_images[0] }}" alt="{{ $product->name }}"
                             class="img-fluid rounded mb-3" style="max-height: 200px;">
                    @else
                        <div class="product-image-placeholder">
                            <div class="text-center">
                                <i class="fas fa-image fs-1 mb-2"></i>
                                <div class="fw-semibold">No Image Available</div>
                                <small class="text-muted">Upload product images to display here</small>
                            </div>
                        </div>
                    @endif

                    @if($product->gallery_images && count($product->gallery_images) > 1)
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-images me-1"></i>
                                {{ count($product->gallery_images) }} images in gallery
                            </small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Stock Status Card -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-warehouse text-primary me-2"></i>
                        <h5 class="mb-0">Inventory Status</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($product->stock_quantity > 10)
                            <div class="bg-success bg-opacity-10 rounded-circle p-3 d-inline-flex mb-2">
                                <i class="fas fa-check-circle text-success fs-2"></i>
                            </div>
                            <div class="fw-bold text-success">In Stock</div>
                        @elseif($product->stock_quantity > 0)
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3 d-inline-flex mb-2">
                                <i class="fas fa-exclamation-triangle text-warning fs-2"></i>
                            </div>
                            <div class="fw-bold text-warning">Low Stock</div>
                        @else
                            <div class="bg-danger bg-opacity-10 rounded-circle p-3 d-inline-flex mb-2">
                                <i class="fas fa-times-circle text-danger fs-2"></i>
                            </div>
                            <div class="fw-bold text-danger">Out of Stock</div>
                        @endif
                        <div class="fs-4 fw-bold mt-2">{{ number_format($product->stock_quantity) }} units</div>
                    </div>

                    @if($product->track_inventory)
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <small>Inventory tracking is enabled for this product</small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tools text-primary me-2"></i>
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning action-btn">
                            <i class="fas fa-edit me-2"></i>Edit Product
                        </a>
                        <button type="button" class="btn btn-info action-btn" onclick="duplicateProduct()">
                            <i class="fas fa-copy me-2"></i>Duplicate Product
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle action-btn w-100" type="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-share-alt me-2"></i>More Actions
                            </button>
                            <ul class="dropdown-menu w-100">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="exportProduct()">
                                        <i class="fas fa-download me-2"></i>Export Data
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="printProduct()">
                                        <i class="fas fa-print me-2"></i>Print Details
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#" onclick="archiveProduct()">
                                        <i class="fas fa-archive me-2"></i>Archive Product
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <hr class="my-3">
                        <button type="button" class="btn btn-danger action-btn delete-product" data-id="{{ $product->id }}">
                            <i class="fas fa-trash me-2"></i>Delete Product
                        </button>
                        <a href="{{ route('products.index') }}" class="btn btn-secondary action-btn">
                            <i class="fas fa-list me-2"></i>All Products
                        </a>
                    </div>
                </div>
            </div>

            <!-- Product Meta Information -->
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info text-primary me-2"></i>
                        <h5 class="mb-0">Meta Information</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Product ID:</span>
                            <span class="fw-semibold">#{{ $product->id }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Created:</span>
                            <span>{{ $product->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Updated:</span>
                            <span>{{ $product->updated_at->format('M d, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Status:</span>
                            <span class="text-capitalize">{{ $product->status }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Delete product with enhanced confirmation
    $('.delete-product').on('click', function() {
        var id = $(this).data('id');
        var productName = '{{ $product->name }}';

        Swal.fire({
            title: 'Delete Product?',
            html: `
                <div class="text-start">
                    <p class="mb-3">You are about to delete:</p>
                    <div class="alert alert-warning">
                        <strong>${productName}</strong><br>
                        <small class="text-muted">SKU: {{ $product->sku }}</small>
                    </div>
                    <p class="text-danger mb-0">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        This action cannot be undone!
                    </p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash me-1"></i>Yes, Delete',
            cancelButtonText: '<i class="fas fa-times me-1"></i>Cancel',
            customClass: {
                popup: 'swal2-popup-custom',
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Deleting Product...',
                    html: 'Please wait while we delete the product.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '/products/' + id,
                    type: 'DELETE',
                    headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                    success: function(res) {
                        if (res.success) {
                            Swal.fire({
                                title: 'Deleted!',
                                text: res.message || 'Product has been deleted successfully.',
                                icon: 'success',
                                confirmButtonColor: '#198754',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                window.location.href = '{{ route("products.index") }}';
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: res.message || 'Failed to delete product',
                                icon: 'error',
                                confirmButtonColor: '#dc3545'
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: xhr.responseJSON?.message || 'Failed to delete product',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            }
        });
    });

    // Add smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});

// Additional action functions
function duplicateProduct() {
    var productName = '{{ $product->name }}';

    Swal.fire({
        title: 'Duplicate Product',
        html: `
            <div class="text-start">
                <p class="mb-3">Create a copy of:</p>
                <div class="alert alert-info">
                    <strong>${productName}</strong>
                </div>
                <p class="text-muted mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    The duplicate will have "(Copy)" added to the name and new SKU/slug will be generated.
                </p>
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#0d6efd',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-copy me-1"></i>Duplicate',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading state
            Swal.fire({
                title: 'Duplicating Product...',
                html: 'Please wait while we create a copy of the product.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '/products/{{ $product->id }}/duplicate',
                type: 'POST',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(res) {
                    if (res.success) {
                        Swal.fire({
                            title: 'Duplicated!',
                            text: res.message || 'Product has been duplicated successfully.',
                            icon: 'success',
                            confirmButtonColor: '#198754',
                            confirmButtonText: 'View Products'
                        }).then(() => {
                            window.location.href = '{{ route("products.index") }}';
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: res.message || 'Failed to duplicate product',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        title: 'Error!',
                        text: xhr.responseJSON?.message || 'Failed to duplicate product',
                        icon: 'error',
                        confirmButtonColor: '#dc3545'
                    });
                }
            });
        }
    });
}

function exportProduct() {
    // Show export options
    Swal.fire({
        title: 'Export Product Data',
        html: `
            <div class="text-start">
                <p class="mb-3">Choose export format:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-success" onclick="exportAs('json')">
                        <i class="fas fa-file-code me-2"></i>JSON Format
                    </button>
                    <button class="btn btn-outline-primary" onclick="exportAs('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV Format
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportAs('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF Report
                    </button>
                </div>
            </div>
        `,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'Close'
    });
}

function exportAs(format) {
    Swal.close();

    var productData = {
        id: {{ $product->id }},
        name: '{{ $product->name }}',
        sku: '{{ $product->sku }}',
        slug: '{{ $product->slug }}',
        description: `{{ $product->description }}`,
        short_description: `{{ $product->short_description }}`,
        amount: {{ $product->amount }},
        cost_price: {{ $product->cost_price ?? 0 }},
        stock_quantity: {{ $product->stock_quantity }},
        weight: {{ $product->weight ?? 0 }},
        status: '{{ $product->status }}',
        is_featured: {{ $product->is_featured ? 'true' : 'false' }},
        track_inventory: {{ $product->track_inventory ? 'true' : 'false' }},
        category: '{{ $product->category ? $product->category->category : 'No Category' }}',
        created_at: '{{ $product->created_at->format('Y-m-d H:i:s') }}',
        updated_at: '{{ $product->updated_at->format('Y-m-d H:i:s') }}'
    };

    if (format === 'json') {
        // Export as JSON
        var dataStr = JSON.stringify(productData, null, 2);
        var dataBlob = new Blob([dataStr], {type: 'application/json'});
        var url = URL.createObjectURL(dataBlob);
        var link = document.createElement('a');
        link.href = url;
        link.download = `product_${productData.sku}_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);

        Swal.fire({
            title: 'Export Complete!',
            text: 'JSON file has been downloaded',
            icon: 'success',
            confirmButtonColor: '#198754'
        });
    } else if (format === 'csv') {
        // Export as CSV
        var csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Field,Value\n";
        Object.keys(productData).forEach(key => {
            var value = productData[key];
            if (typeof value === 'string' && value.includes(',')) {
                value = `"${value}"`;
            }
            csvContent += `${key},${value}\n`;
        });

        var encodedUri = encodeURI(csvContent);
        var link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `product_${productData.sku}_${new Date().toISOString().split('T')[0]}.csv`);
        link.click();

        Swal.fire({
            title: 'Export Complete!',
            text: 'CSV file has been downloaded',
            icon: 'success',
            confirmButtonColor: '#198754'
        });
    } else if (format === 'pdf') {
        // For PDF, we'll open the print dialog with special print styles
        Swal.fire({
            title: 'PDF Export',
            text: 'Opening print dialog. Please select "Save as PDF" as your printer.',
            icon: 'info',
            confirmButtonColor: '#0d6efd',
            confirmButtonText: 'Open Print Dialog'
        }).then(() => {
            window.print();
        });
    }
}

function printProduct() {
    window.print();
}

function archiveProduct() {
    Swal.fire({
        title: 'Archive Product',
        text: 'This will move the product to archived status. It can be restored later.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#fd7e14',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-archive me-1"></i>Archive',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Feature Coming Soon',
                text: 'Product archiving feature will be available in the next update.',
                icon: 'info',
                confirmButtonColor: '#0d6efd'
            });
        }
    });
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
/* Print styles */
@media print {
    .btn, .card-header, .breadcrumb, .action-btn {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    .product-header {
        background: #f8f9fa !important;
        color: #212529 !important;
        -webkit-print-color-adjust: exact;
    }

    .stat-card {
        border: 1px solid #dee2e6 !important;
        margin-bottom: 1rem !important;
    }
}

/* Custom SweetAlert2 styles */
.swal2-popup-custom {
    border-radius: 0.75rem;
}

.swal2-popup .btn {
    margin: 0 0.25rem;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
}
</style>
@endsection
