/**
 * Universal Helper Functions for RX-Info Application
 * Contains reusable JavaScript functions for common operations
 */

/**
 * Universal Delete Function
 * @param {Object} options - Configuration object
 * @param {string} options.id - The ID of the item to delete
 * @param {string} options.url - The delete URL endpoint
 * @param {string} options.itemName - Name of the item being deleted (e.g., 'drug', 'user', 'category')
 * @param {Object} options.table - DataTable instance to reload after deletion
 * @param {string} [options.confirmTitle] - Custom confirmation title
 * @param {string} [options.confirmText] - Custom confirmation text
 * @param {string} [options.loadingTitle] - Custom loading title
 * @param {string} [options.loadingText] - Custom loading text
 * @param {string} [options.successTitle] - Custom success title
 * @param {string} [options.successMessage] - Custom success message
 * @param {Function} [options.onSuccess] - Custom success callback
 * @param {Function} [options.onError] - Custom error callback
 */
function universalDelete(options) {
    // Validate required parameters
    if (!options.id || !options.url || !options.itemName) {
        console.error('universalDelete: Missing required parameters (id, url, itemName)');
        return;
    }

    // Default configuration
    const config = {
        confirmTitle: options.confirmTitle || 'Are you sure?',
        confirmText: options.confirmText || "You won't be able to revert this action!",
        loadingTitle: options.loadingTitle || 'Deleting...',
        loadingText: options.loadingText || `Please wait while we delete the ${options.itemName}.`,
        successTitle: options.successTitle || 'Deleted!',
        successMessage: options.successMessage || `${options.itemName.charAt(0).toUpperCase() + options.itemName.slice(1)} has been deleted successfully.`,
        ...options
    };

    Swal.fire({
        title: config.confirmTitle,
        text: config.confirmText,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: config.loadingTitle,
                text: config.loadingText,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Perform delete request
            $.ajax({
                url: config.url.replace(':id', config.id),
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    const successMessage = response.message || config.successMessage;

                    Swal.fire({
                        icon: 'success',
                        title: config.successTitle,
                        text: successMessage,
                        timer: 2000,
                        showConfirmButton: false
                    });

                    // Custom success callback or default table reload
                    if (config.onSuccess && typeof config.onSuccess === 'function') {
                        config.onSuccess(response);
                    } else if (config.table) {
                        config.table.ajax.reload(null, false);
                    }
                },
                error: function(xhr) {
                    let errorMessage = `An error occurred while deleting the ${config.itemName}.`;
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: errorMessage
                    });

                    // Custom error callback
                    if (config.onError && typeof config.onError === 'function') {
                        config.onError(xhr);
                    }
                }
            });
        }
    });
}

/**
 * Universal Refresh Table Function
 * @param {Object} table - DataTable instance
 * @param {string} [message] - Custom success message
 */
function refreshTable(table, message = 'Table data has been refreshed.') {
    if (table && typeof table.ajax !== 'undefined') {
        table.ajax.reload(null, false);
        Swal.fire({
            icon: 'success',
            title: 'Refreshed!',
            text: message,
            timer: 1500,
            showConfirmButton: false
        });
    } else {
        console.error('refreshTable: Invalid table instance provided');
    }
}

/**
 * Universal Status Toggle Function
 * @param {Object} options - Configuration object
 * @param {string} options.id - The ID of the item
 * @param {string} options.url - The status toggle URL endpoint
 * @param {string} options.itemName - Name of the item
 * @param {Object} options.table - DataTable instance to reload
 * @param {string} [options.currentStatus] - Current status of the item
 */
function toggleStatus(options) {
    if (!options.id || !options.url || !options.itemName) {
        console.error('toggleStatus: Missing required parameters (id, url, itemName)');
        return;
    }

    const action = options.currentStatus === 'active' ? 'deactivate' : 'activate';

    Swal.fire({
        title: `${action.charAt(0).toUpperCase() + action.slice(1)} ${options.itemName}?`,
        text: `Are you sure you want to ${action} this ${options.itemName}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: `Yes, ${action} it!`,
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: options.url.replace(':id', options.id),
                type: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message || `${options.itemName} status updated successfully.`,
                        timer: 2000,
                        showConfirmButton: false
                    });

                    if (options.table) {
                        options.table.ajax.reload(null, false);
                    }
                },
                error: function(xhr) {
                    let errorMessage = `An error occurred while updating the ${options.itemName} status.`;
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: errorMessage
                    });
                }
            });
        }
    });
}

/**
 * Show loading overlay
 * @param {string} [message] - Loading message
 */
function showLoading(message = 'Loading...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

/**
 * Hide loading overlay
 */
function hideLoading() {
    Swal.close();
}

/**
 * Show success message
 * @param {string} title - Success title
 * @param {string} [text] - Success message
 * @param {number} [timer] - Auto close timer in milliseconds
 */
function showSuccess(title, text = '', timer = 2000) {
    Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        timer: timer,
        showConfirmButton: false
    });
}

/**
 * Show error message
 * @param {string} title - Error title
 * @param {string} [text] - Error message
 */
function showError(title, text = '') {
    Swal.fire({
        icon: 'error',
        title: title,
        text: text
    });
}

/**
 * Universal DataTable Initialization Function
 * @param {Object} options - Configuration object
 * @param {string} options.tableId - The ID of the table element
 * @param {string} options.ajaxUrl - The AJAX URL for data
 * @param {Array} options.columns - Array of column definitions
 * @param {string} [options.itemName] - Name of the items (for empty messages)
 * @param {number} [options.pageLength] - Default page length
 * @param {Array} [options.lengthMenu] - Length menu options
 * @param {Array} [options.order] - Default ordering
 * @param {Object} [options.language] - Custom language settings
 * @param {string} [options.dom] - Custom DOM layout
 * @param {Function} [options.drawCallback] - Custom draw callback
 * @param {Function} [options.ajaxData] - Custom AJAX data function
 * @param {Function} [options.ajaxError] - Custom AJAX error handler
 * @param {boolean} [options.processing] - Show processing indicator
 * @param {boolean} [options.serverSide] - Enable server-side processing
 * @param {boolean} [options.responsive] - Enable responsive extension
 * @returns {Object} DataTable instance
 */
function initializeDataTable(options) {
    // Validate required parameters
    if (!options.tableId || !options.ajaxUrl || !options.columns) {
        console.error('initializeDataTable: Missing required parameters (tableId, ajaxUrl, columns)');
        return null;
    }

    // Default configuration
    const config = {
        processing: options.processing !== false, // Default true
        serverSide: options.serverSide !== false, // Default true
        responsive: options.responsive !== false, // Default true
        scrollX: options.scrollX || false, // Default false
        pageLength: options.pageLength || 25,
        lengthMenu: options.lengthMenu || [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        order: options.order || [[0, 'desc']],
        itemName: options.itemName || 'record',

        // AJAX configuration
        ajax: {
            url: options.ajaxUrl,
            type: 'GET',
            data: options.ajaxData || function(d) {
                // Default empty data function
                return d;
            },
            error: options.ajaxError || function(xhr, error, thrown) {
                console.error('DataTable AJAX Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error Loading Data',
                    text: `Failed to load ${config.itemName} data. Please refresh the page.`,
                    confirmButtonText: 'Refresh Page'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    }
                });
            }
        },

        // Columns
        columns: options.columns,

        // DOM layout
        dom: options.dom || '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',

        // Language settings
        language: options.language || {
            processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
            emptyTable: `<div class="text-center py-4"><i class="fas fa-database fa-3x text-muted mb-3"></i><br><span class="text-muted">No ${config.itemName}s found</span></div>`,
            zeroRecords: '<div class="text-center py-4"><i class="fas fa-search fa-3x text-muted mb-3"></i><br><span class="text-muted">No matching records found</span></div>',
            lengthMenu: "Show _MENU_ entries",
            search: "Search:",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            },
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)"
        },

        // Draw callback
        drawCallback: options.drawCallback || function(settings) {
            // Reinitialize tooltips after each draw
            $('[title]').tooltip();

            // Add loading state management
            $(`#${options.tableId}`).removeClass('table-loading');
        }
    };

    // Initialize DataTable
    const table = $(`#${options.tableId}`).DataTable(config);

    // Add loading state when table is processing
    table.on('processing.dt', function(e, settings, processing) {
        if (processing) {
            $(`#${options.tableId}`).addClass('table-loading');
        } else {
            $(`#${options.tableId}`).removeClass('table-loading');
        }
    });

    // Initialize tooltips
    $('[title]').tooltip();

    return table;
}

/**
 * Get standard column definitions for common column types
 * @param {string} type - Column type ('index', 'actions', 'status', 'date', 'text')
 * @param {Object} [options] - Additional options for the column
 * @returns {Object} Column definition
 */
function getColumnDefinition(type, options = {}) {
    const definitions = {
        index: {
            data: 'DT_RowIndex',
            name: 'DT_RowIndex',
            orderable: false,
            searchable: false,
            className: 'text-center',
            ...options
        },
        actions: {
            data: 'actions',
            name: 'actions',
            orderable: false,
            searchable: false,
            className: 'text-center',
            ...options
        },
        status: {
            data: 'status',
            name: 'status',
            orderable: false,
            className: 'text-center',
            ...options
        },
        date: {
            className: 'text-center',
            ...options
        },
        text: {
            ...options
        }
    };

    return definitions[type] || options;
}

/**
 * Universal Image Preview Function
 * @param {Object} options - Configuration object
 * @param {string} options.inputId - ID of the file input element
 * @param {string} options.previewId - ID of the preview container
 * @param {string} options.imageId - ID of the preview image element
 * @param {Array} [options.allowedTypes] - Allowed file types
 * @param {number} [options.maxSize] - Maximum file size in bytes
 * @param {string} [options.maxWidth] - Maximum preview width
 * @param {string} [options.maxHeight] - Maximum preview height
 */
function initializeImagePreview(options) {
    // Validate required parameters
    if (!options.inputId || !options.previewId || !options.imageId) {
        console.error('initializeImagePreview: Missing required parameters (inputId, previewId, imageId)');
        return;
    }

    // Default configuration
    const config = {
        allowedTypes: options.allowedTypes || ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        maxSize: options.maxSize || (2 * 1024 * 1024), // 2MB default
        maxWidth: options.maxWidth || '200px',
        maxHeight: options.maxHeight || '200px',
        ...options
    };

    $(`#${config.inputId}`).on('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file type
            if (!config.allowedTypes.includes(file.type)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File Type',
                    text: `Please select a valid image file (${config.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}).`
                });
                $(this).val('');
                $(`#${config.previewId}`).hide();
                return;
            }

            // Validate file size
            if (file.size > config.maxSize) {
                const sizeMB = (config.maxSize / (1024 * 1024)).toFixed(1);
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: `Please select an image smaller than ${sizeMB}MB.`
                });
                $(this).val('');
                $(`#${config.previewId}`).hide();
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                $(`#${config.imageId}`)
                    .attr('src', e.target.result)
                    .css({
                        'max-width': config.maxWidth,
                        'max-height': config.maxHeight
                    });
                $(`#${config.previewId}`).show();
            };
            reader.readAsDataURL(file);
        } else {
            $(`#${config.previewId}`).hide();
        }
    });
}

/**
 * Universal Form Submission Function
 * @param {Object} options - Configuration object
 * @param {string} options.formId - ID of the form element
 * @param {string} options.submitBtnId - ID of the submit button
 * @param {string} [options.successMessage] - Success message
 * @param {string} [options.redirectUrl] - URL to redirect after success
 * @param {boolean} [options.hasFileUpload] - Whether form has file uploads
 * @param {Function} [options.onSuccess] - Custom success callback
 * @param {Function} [options.onError] - Custom error callback
 * @param {Function} [options.beforeSubmit] - Callback before form submission
 */
function initializeFormSubmission(options) {
    // Validate required parameters
    if (!options.formId || !options.submitBtnId) {
        console.error('initializeFormSubmission: Missing required parameters (formId, submitBtnId)');
        return;
    }

    // Default configuration
    const config = {
        successMessage: options.successMessage || 'Data has been saved successfully.',
        hasFileUpload: options.hasFileUpload || false,
        ...options
    };

    // Mark form as using initializeFormSubmission
    $(`#${config.formId}`).data('form-submission-initialized', true);

    // Remove any existing submit handlers to prevent conflicts
    $(`#${config.formId}`).off('submit.formSubmission submit.ckeditor');

    $(`#${config.formId}`).on('submit.formSubmission', function(e) {
        e.preventDefault();

        // Sync CKEditor data immediately before any processing
        syncAllCKEditorsInForm(`#${config.formId}`);

        // Validate CKEditor fields
        if (!validateCKEditorsInForm(`#${config.formId}`)) {
            return; // Stop submission if validation fails
        }

        // Execute before submit callback
        if (config.beforeSubmit && typeof config.beforeSubmit === 'function') {
            const shouldContinue = config.beforeSubmit();
            if (shouldContinue === false) return;
        }

        // Show loading state
        const submitBtn = $(`#${config.submitBtnId}`);
        submitBtn.attr('disabled', true);
        submitBtn.find('.indicator-label').hide();
        submitBtn.find('.indicator-progress').show();

        // Prepare form data
        const formData = config.hasFileUpload ? new FormData(this) : $(this).serialize();

        // AJAX configuration
        const ajaxConfig = {
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            success: function(response) {
                if (config.onSuccess && typeof config.onSuccess === 'function') {
                    config.onSuccess(response);
                } else {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: config.successMessage,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        if (config.redirectUrl) {
                            window.location.href = config.redirectUrl;
                        }
                    });
                }
            },
            error: function(xhr) {
                // Reset button state
                submitBtn.attr('disabled', false);
                submitBtn.find('.indicator-label').show();
                submitBtn.find('.indicator-progress').hide();

                if (config.onError && typeof config.onError === 'function') {
                    config.onError(xhr);
                } else {
                    handleFormErrors(xhr);
                }
            }
        };

        // Add file upload specific settings
        if (config.hasFileUpload) {
            ajaxConfig.processData = false;
            ajaxConfig.contentType = false;
        }

        $.ajax(ajaxConfig);
    });
}

/**
 * Handle form validation errors
 * @param {Object} xhr - XMLHttpRequest object
 */
function handleFormErrors(xhr) {
    if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;

        // Clear previous error states
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Display validation errors
        $.each(errors, function(field, messages) {
            const input = $(`[name="${field}"]`);
            input.addClass('is-invalid');
            input.after(`<div class="invalid-feedback">${messages[0]}</div>`);
        });

        Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'Please check the form for errors and try again.',
            confirmButtonText: 'OK'
        });
    } else {
        // Server error
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: xhr.responseJSON?.message || 'An error occurred while processing your request.',
            confirmButtonText: 'OK'
        });
    }
}

/**
 * Initialize auto-resize textareas
 * @param {string} [selector] - CSS selector for textareas (default: 'textarea')
 */
function initializeAutoResizeTextareas(selector = 'textarea') {
    $(selector).each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
}

/**
 * Initialize character counter for textareas
 * @param {string} [selector] - CSS selector for textareas (default: 'textarea[maxlength]')
 */
function initializeCharacterCounter(selector = 'textarea[maxlength]') {
    $(selector).on('input', function() {
        const maxLength = $(this).attr('maxlength');
        if (maxLength) {
            const currentLength = $(this).val().length;
            const counter = $(this).siblings('.char-counter');
            if (counter.length === 0) {
                $(this).after(`<div class="char-counter text-muted fs-7 mt-1">${currentLength}/${maxLength} characters</div>`);
            } else {
                counter.text(`${currentLength}/${maxLength} characters`);
            }
        }
    });
}

/**
 * Initialize unsaved changes warning
 * @param {Object} options - Configuration object
 * @param {string} options.formId - ID of the form to monitor
 * @param {string} options.exitSelector - CSS selector for exit links/buttons
 * @param {string} [options.warningTitle] - Custom warning title
 * @param {string} [options.warningText] - Custom warning text
 */
function initializeUnsavedChangesWarning(options) {
    if (!options.formId || !options.exitSelector) {
        console.error('initializeUnsavedChangesWarning: Missing required parameters (formId, exitSelector)');
        return;
    }

    const config = {
        warningTitle: options.warningTitle || 'Are you sure?',
        warningText: options.warningText || 'You have unsaved changes. Do you want to leave without saving?',
        ...options
    };

    $(document).on('click', config.exitSelector, function(e) {
        const form = $(`#${config.formId}`);
        let hasData = false;

        // Check if form has any data
        form.find('input, textarea, select').each(function() {
            if ($(this).val() && $(this).val().trim() !== '') {
                hasData = true;
                return false;
            }
        });

        if (hasData) {
            e.preventDefault();
            const href = $(this).attr('href');

            Swal.fire({
                title: config.warningTitle,
                text: config.warningText,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, leave',
                cancelButtonText: 'Stay'
            }).then((result) => {
                if (result.isConfirmed && href) {
                    window.location.href = href;
                }
            });
        }
    });
}

/**
 * Initialize form with all common enhancements
 * @param {Object} options - Configuration object
 * @param {string} options.formId - ID of the form
 * @param {string} options.submitBtnId - ID of the submit button
 * @param {string} [options.imageInputId] - ID of image input (if any)
 * @param {string} [options.imagePreviewId] - ID of image preview container
 * @param {string} [options.previewImageId] - ID of preview image element
 * @param {string} [options.exitSelector] - Selector for exit links
 * @param {string} [options.successMessage] - Success message
 * @param {string} [options.redirectUrl] - Redirect URL after success
 * @param {boolean} [options.hasFileUpload] - Whether form has file uploads
 * @param {boolean} [options.enableCKEditor] - Whether to enable CKEditor for textareas (default: true)
 * @param {Function} [options.onSuccess] - Custom success callback
 * @param {Function} [options.onError] - Custom error callback
 */
function initializeEnhancedForm(options) {
    if (!options.formId || !options.submitBtnId) {
        console.error('initializeEnhancedForm: Missing required parameters (formId, submitBtnId)');
        return;
    }

    // Initialize form submission
    initializeFormSubmission({
        formId: options.formId,
        submitBtnId: options.submitBtnId,
        successMessage: options.successMessage,
        redirectUrl: options.redirectUrl,
        hasFileUpload: options.hasFileUpload,
        onSuccess: options.onSuccess,
        onError: options.onError
    });

    // Initialize image preview if specified
    if (options.imageInputId && options.imagePreviewId && options.previewImageId) {
        initializeImagePreview({
            inputId: options.imageInputId,
            previewId: options.imagePreviewId,
            imageId: options.previewImageId
        });
    }

    // Initialize CKEditor for all textareas in the form (unless disabled)
    if (options.enableCKEditor !== false) {
        // Use optimized form-specific initialization
        const formTextareas = $(`#${options.formId}`).find('textarea');
        if (formTextareas.length > 0) {
            // Initialize CKEditor immediately for this form
            initializeCKEditorForForm(`#${options.formId}`, function() {
                // CKEditor initialization complete
            });
        }
    }

    // Initialize auto-resize textareas (only for non-CKEditor textareas)
    if (options.enableCKEditor === false) {
        initializeAutoResizeTextareas();
    }

    // Initialize character counter
    initializeCharacterCounter();

    // Initialize unsaved changes warning if exit selector provided
    if (options.exitSelector) {
        initializeUnsavedChangesWarning({
            formId: options.formId,
            exitSelector: options.exitSelector
        });
    }

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Initialize Select2 for drug dropdowns if they exist
    initializeDrugSelect2();
}

/**
 * Initialize Select2 for drug dropdowns with search functionality
 * @param {string} [selector] - CSS selector for drug select elements (default: 'select[name="drug_id"]')
 * @param {Object} [options] - Additional Select2 options
 */
function initializeDrugSelect2(selector = 'select[name="drug_id"]', options = {}) {
    const defaultOptions = {
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Select Drug',
        allowClear: true,
        searchInputPlaceholder: 'Search for a drug...',
        language: {
            noResults: function() {
                return "No drugs found";
            },
            searching: function() {
                return "Searching...";
            }
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Only initialize if the selector exists on the page
    if ($(selector).length > 0) {
        $(selector).select2(finalOptions);
    }
}

/**
 * Handle notification box toggle + outside click
 */
function handleNotificationBox(event) {
    const box = document.getElementById("notificationBox");
    const button = document.getElementById("notificationButton");

    // If click is on button -> toggle box
    if (button.contains(event.target)) {
        box.classList.toggle("d-none");
        return;
    }

    // If box is open and click happens outside -> close it
    if (!box.classList.contains("d-none") && !box.contains(event.target)) {
        box.classList.add("d-none");
    }
}

// Attach once on document
document.addEventListener("click", handleNotificationBox);

/**
 * Initialize CKEditor for specified textarea elements
 * @param {string} selector - CSS selector for textarea elements (default: '.ckeditor')
 * @param {Object} [options] - CKEditor configuration options
 */
function initializeCKEditor(selector = '.ckeditor', options = {}) {
    // Default CKEditor configuration
    const defaultConfig = {
        toolbar: {
            items: [
                'heading', '|',
                'bold', 'italic', 'underline', 'strikethrough', '|',
                'fontSize', 'fontColor', 'fontBackgroundColor', '|',
                'alignment', '|',
                'numberedList', 'bulletedList', '|',
                'outdent', 'indent', '|',
                'link', 'insertImage', 'insertTable', '|',
                'blockQuote', 'codeBlock', '|',
                'horizontalLine', '|',
                'undo', 'redo', '|',
                'sourceEditing'
            ],
            shouldNotGroupWhenFull: true
        },
        // Set minimum height for the editor
        height: '200px',
        heading: {
            options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
            ]
        },
        fontSize: {
            options: [
                9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36
            ]
        },
        fontColor: {
            colors: [
                {
                    color: 'hsl(0, 0%, 0%)',
                    label: 'Black'
                },
                {
                    color: 'hsl(0, 0%, 30%)',
                    label: 'Dim grey'
                },
                {
                    color: 'hsl(0, 0%, 60%)',
                    label: 'Grey'
                },
                {
                    color: 'hsl(0, 0%, 90%)',
                    label: 'Light grey'
                },
                {
                    color: 'hsl(0, 0%, 100%)',
                    label: 'White',
                    hasBorder: true
                },
                {
                    color: 'hsl(0, 75%, 60%)',
                    label: 'Red'
                },
                {
                    color: 'hsl(30, 75%, 60%)',
                    label: 'Orange'
                },
                {
                    color: 'hsl(60, 75%, 60%)',
                    label: 'Yellow'
                },
                {
                    color: 'hsl(90, 75%, 60%)',
                    label: 'Light green'
                },
                {
                    color: 'hsl(120, 75%, 60%)',
                    label: 'Green'
                },
                {
                    color: 'hsl(150, 75%, 60%)',
                    label: 'Aquamarine'
                },
                {
                    color: 'hsl(180, 75%, 60%)',
                    label: 'Turquoise'
                },
                {
                    color: 'hsl(210, 75%, 60%)',
                    label: 'Light blue'
                },
                {
                    color: 'hsl(240, 75%, 60%)',
                    label: 'Blue'
                },
                {
                    color: 'hsl(270, 75%, 60%)',
                    label: 'Purple'
                }
            ]
        },
        fontBackgroundColor: {
            colors: [
                {
                    color: 'hsl(0, 0%, 100%)',
                    label: 'White',
                    hasBorder: true
                },
                {
                    color: 'hsl(0, 0%, 90%)',
                    label: 'Light grey'
                },
                {
                    color: 'hsl(0, 0%, 60%)',
                    label: 'Grey'
                },
                {
                    color: 'hsl(0, 0%, 30%)',
                    label: 'Dim grey'
                },
                {
                    color: 'hsl(0, 0%, 0%)',
                    label: 'Black'
                },
                {
                    color: 'hsl(0, 75%, 60%)',
                    label: 'Red'
                },
                {
                    color: 'hsl(30, 75%, 60%)',
                    label: 'Orange'
                },
                {
                    color: 'hsl(60, 75%, 60%)',
                    label: 'Yellow'
                },
                {
                    color: 'hsl(90, 75%, 60%)',
                    label: 'Light green'
                },
                {
                    color: 'hsl(120, 75%, 60%)',
                    label: 'Green'
                },
                {
                    color: 'hsl(150, 75%, 60%)',
                    label: 'Aquamarine'
                },
                {
                    color: 'hsl(180, 75%, 60%)',
                    label: 'Turquoise'
                },
                {
                    color: 'hsl(210, 75%, 60%)',
                    label: 'Light blue'
                },
                {
                    color: 'hsl(240, 75%, 60%)',
                    label: 'Blue'
                },
                {
                    color: 'hsl(270, 75%, 60%)',
                    label: 'Purple'
                }
            ]
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells',
                'tableCellProperties',
                'tableProperties'
            ]
        },
        image: {
            toolbar: [
                'imageTextAlternative',
                'toggleImageCaption',
                'imageStyle:inline',
                'imageStyle:block',
                'imageStyle:side'
            ]
        },
        link: {
            decorators: {
                addTargetToExternalLinks: true,
                defaultProtocol: 'https://',
                toggleDownloadable: {
                    mode: 'manual',
                    label: 'Downloadable',
                    attributes: {
                        download: 'file'
                    }
                }
            }
        },

        // Optimized configuration for faster initialization
        typing: {
            transformations: {
                remove: [
                    // Remove transformations for faster loading
                    'oneHalf', 'oneThird', 'twoThirds', 'oneForth', 'threeForths',
                    'quoteIntroduction', 'arrowLeft', 'arrowRight'
                ]
            }
        },

        // Simplified HTML support for faster loading
        htmlSupport: {
            allow: [
                {
                    name: /^(p|h[1-6]|strong|em|u|s|a|ul|ol|li|br|blockquote|table|thead|tbody|tr|td|th)$/,
                    attributes: true,
                    classes: true,
                    styles: false // Disable inline styles for faster loading
                }
            ]
        },

        // Disable some features for faster initialization
        removePlugins: [
            'MediaEmbedToolbar' // Remove media embed for faster loading
        ],

        // Language and localization
        language: 'en',

        placeholder: 'Enter your content here...',
        ...options
    };

    // Initialize CKEditor for each matching element
    $(selector).each(function() {
        const element = this;
        const elementId = $(element).attr('id');

        if (elementId && !window.ckeditorInstances) {
            window.ckeditorInstances = {};
        }

        // Check if CKEditor is already initialized for this element
        if (elementId && window.ckeditorInstances && window.ckeditorInstances[elementId]) {
            return;
        }

        ClassicEditor
            .create(element, defaultConfig)
            .then(editor => {
                // Store editor instance for later reference
                if (elementId) {
                    if (!window.ckeditorInstances) {
                        window.ckeditorInstances = {};
                    }
                    window.ckeditorInstances[elementId] = editor;
                }

                // Handle form submission to sync CKEditor data
                const form = $(element).closest('form');
                if (form.length) {
                    // Remove HTML5 required attribute from the textarea since CKEditor handles validation
                    if ($(element).prop('required')) {
                        $(element).removeAttr('required');
                        // Store the required state for custom validation
                        $(element).data('ckeditor-required', true);
                    }

                    // Only add form handler if not already added by initializeFormSubmission
                    if (!form.data('ckeditor-handler-added')) {
                        form.data('ckeditor-handler-added', true);

                        // Remove any existing CKEditor submit handlers
                        form.off('submit.ckeditor');

                        form.on('submit.ckeditor', function(e) {
                            // Only handle if this form doesn't use initializeFormSubmission
                            if (!form.data('form-submission-initialized')) {
                                e.preventDefault();

                                const formElement = this;
                                const $form = $(formElement);
                                const formId = $form.attr('id');

                                // Sync and validate CKEditor data
                                syncAllCKEditorsInForm(`#${formId}`);

                                if (validateCKEditorsInForm(`#${formId}`)) {
                                    // Remove the event handler to prevent infinite loop
                                    $form.off('submit.ckeditor');
                                    // Submit the form
                                    formElement.submit();
                                }
                            }
                        });
                    }
                }

                // Also sync data on form reset
                if (form.length) {
                    form.on('reset', function() {
                        setTimeout(function() {
                            editor.setData('');
                        }, 100);
                    });
                }

                // Ensure proper styling is applied
                const editorElement = $(element).next('.ck-editor');
                if (editorElement.length) {
                    // Apply form control classes to the editor
                    if ($(element).hasClass('form-control-solid')) {
                        editorElement.addClass('form-control-solid-editor');
                    }
                    if ($(element).hasClass('is-invalid')) {
                        editorElement.addClass('is-invalid-editor');
                    }
                    if ($(element).hasClass('is-valid')) {
                        editorElement.addClass('is-valid-editor');
                    }

                    // Ensure border is visible
                    editorElement.css({
                        'border': '1px solid #e4e6ef',
                        'border-radius': '0.475rem'
                    });
                }

                // Force border application after a short delay
                setTimeout(function() {
                    const editorElement = $(element).next('.ck-editor');
                    if (editorElement.length) {
                        editorElement.css({
                            'border': '1px solid #e4e6ef !important',
                            'border-radius': '0.475rem !important',
                            'box-shadow': 'none !important'
                        });
                    }
                }, 100);

                // CKEditor initialized successfully
            })
            .catch(error => {
                // Error initializing CKEditor - fail silently in production
            });
    });
}

/**
 * Destroy CKEditor instance
 * @param {string} elementId - ID of the textarea element
 */
function destroyCKEditor(elementId) {
    if (window.ckeditorInstances && window.ckeditorInstances[elementId]) {
        window.ckeditorInstances[elementId].destroy()
            .then(() => {
                delete window.ckeditorInstances[elementId];
                // CKEditor destroyed
            })
            .catch(error => {
                // Error destroying CKEditor - fail silently
            });
    }
}

/**
 * Initialize CKEditor for all textareas within a container
 * @param {string} [containerSelector] - CSS selector for container (default: 'body')
 * @param {Object} [options] - CKEditor configuration options
 */
function initializeAllCKEditors(containerSelector = 'body', options = {}) {
    const container = $(containerSelector);

    // Find all textareas within the container
    container.find('textarea').each(function() {
        const textarea = $(this);
        const textareaId = textarea.attr('id');

        // Skip if textarea doesn't have an ID (required for CKEditor)
        if (!textareaId) {
            return;
        }

        // Skip if CKEditor is already initialized for this textarea
        if (window.ckeditorInstances && window.ckeditorInstances[textareaId]) {
            return;
        }

        // Skip textareas that are explicitly marked to skip CKEditor
        if (textarea.hasClass('no-ckeditor') || textarea.data('no-ckeditor')) {
            return;
        }

        // Initialize CKEditor for this textarea
        initializeCKEditor(`#${textareaId}`, options);
    });
}

/**
 * Initialize CKEditor globally for all textareas on page load
 */
function initializeGlobalCKEditor() {
    $(document).ready(function() {
        // Wait a bit for DOM to be fully ready
        setTimeout(function() {
            // Initialize CKEditor for all textareas that need it
            initializeAllCKEditors('body');

            // Enforce borders after initialization
            setTimeout(function() {
                enforceCKEditorBorders();
            }, 200);
        }, 100);
    });
}

/**
 * Sync all CKEditor instances with their textareas before form submission
 * @param {string} formSelector - CSS selector for the form
 */
function syncAllCKEditorsInForm(formSelector) {
    const form = $(formSelector);

    form.find('textarea').each(function() {
        const textarea = $(this);
        const textareaId = textarea.attr('id');

        if (textareaId && window.ckeditorInstances && window.ckeditorInstances[textareaId]) {
            try {
                // Update textarea value with CKEditor content
                const editorData = window.ckeditorInstances[textareaId].getData();
                textarea.val(editorData);

                // Trigger change event to notify other scripts
                textarea.trigger('change');

                // CKEditor data synced
            } catch (error) {
                // Error syncing CKEditor - fail silently
            }
        }
    });
}

/**
 * Validate all CKEditor instances in a form
 * @param {string} formSelector - CSS selector for the form
 * @returns {boolean} - True if all required CKEditor fields are filled
 */
function validateCKEditorsInForm(formSelector) {
    const form = $(formSelector);
    let isValid = true;
    let firstInvalidField = null;

    // Check both data-ckeditor-required and original required textareas
    form.find('textarea').each(function() {
        const textarea = $(this);
        const textareaId = textarea.attr('id');
        const isRequired = textarea.data('ckeditor-required') === true || textarea.prop('required');

        if (isRequired && textareaId && window.ckeditorInstances && window.ckeditorInstances[textareaId]) {
            const editorData = window.ckeditorInstances[textareaId].getData();
            const cleanData = editorData.replace(/<[^>]*>/g, '').trim(); // Remove HTML tags for validation

            if (!cleanData || cleanData === '') {
                isValid = false;
                if (!firstInvalidField) {
                    // Try multiple ways to find the label
                    let label = textarea.prev('label').text() ||
                               textarea.closest('.fv-row').find('label').text() ||
                               textarea.closest('.form-group').find('label').text() ||
                               textarea.attr('placeholder') ||
                               'Required field';

                    // Clean up label text
                    label = label.replace(/\s*\*\s*$/, '').trim();

                    firstInvalidField = {
                        editor: window.ckeditorInstances[textareaId],
                        label: label,
                        textarea: textarea
                    };
                }
            }
        }
    });

    if (!isValid && firstInvalidField) {
        // Focus on the first invalid field
        try {
            firstInvalidField.editor.editing.view.focus();
        } catch (e) {
            // Could not focus CKEditor - fail silently
        }

        // Show validation message
        Swal.fire({
            icon: 'warning',
            title: 'Required Field Missing',
            text: `Please fill in the "${firstInvalidField.label}" field.`,
            confirmButtonText: 'OK'
        });
    }

    return isValid;
}

/**
 * Force border visibility for all CKEditor instances
 * Call this function if borders are not showing properly
 */
function enforceCKEditorBorders() {
    // Apply borders to all CKEditor instances
    $('.ck-editor').each(function() {
        const $editor = $(this);

        // Apply base border styles
        $editor.css({
            'border': '1px solid #e4e6ef !important',
            'border-radius': '0.475rem !important',
            'box-shadow': 'none !important',
            'overflow': 'hidden'
        });

        // Check for associated textarea classes
        const $textarea = $editor.prev('textarea');
        if ($textarea.length) {
            if ($textarea.hasClass('form-control-solid')) {
                $editor.css('background-color', '#f8f9fa');
            }
            if ($textarea.hasClass('is-invalid')) {
                $editor.css({
                    'border-color': '#dc3545 !important',
                    'box-shadow': '0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important'
                });
            }
            if ($textarea.hasClass('is-valid')) {
                $editor.css({
                    'border-color': '#28a745 !important',
                    'box-shadow': '0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important'
                });
            }
        }
    });

    // CKEditor borders enforced
}

/**
 * Initialize CKEditor content display styling
 * Call this function to ensure proper styling of CKEditor content in show pages
 */
function initializeCKEditorContentDisplay() {
    $('.ckeditor-content').each(function() {
        const $content = $(this);

        // Add proper styling classes
        $content.addClass('rendered-content');

        // Handle empty content
        if (!$content.html().trim() || $content.html().trim() === '') {
            $content.html('<em class="text-muted">No content available</em>');
        }

        // Ensure links open in new tab for external links
        $content.find('a[href^="http"]').attr('target', '_blank').attr('rel', 'noopener noreferrer');

        // Add responsive classes to tables
        $content.find('table').addClass('table table-bordered table-sm');

        // Add Bootstrap classes to images
        $content.find('img').addClass('img-fluid');
    });

    // CKEditor content display initialized
}

/**
 * Fast CKEditor initialization for immediate form submission
 * This function initializes CKEditor instances immediately when called
 * @param {string} [containerSelector] - Optional container selector to limit scope
 */
function fastInitializeCKEditor(containerSelector = 'body') {
    const container = $(containerSelector);

    container.find('textarea').each(function() {
        const textarea = $(this);
        const textareaId = textarea.attr('id');

        // Skip if no ID or already initialized
        if (!textareaId || (window.ckeditorInstances && window.ckeditorInstances[textareaId])) {
            return;
        }

        // Skip if explicitly marked to skip CKEditor
        if (textarea.hasClass('no-ckeditor') || textarea.data('no-ckeditor')) {
            return;
        }

        // Initialize immediately with minimal config for speed
        const fastConfig = {
            toolbar: {
                items: ['bold', 'italic', 'underline', '|', 'numberedList', 'bulletedList', '|', 'undo', 'redo'],
                shouldNotGroupWhenFull: false
            },
            height: '150px',
            placeholder: textarea.attr('placeholder') || 'Enter your content here...',
            language: 'en'
        };

        ClassicEditor
            .create(this, fastConfig)
            .then(editor => {
                // Store editor instance
                if (!window.ckeditorInstances) {
                    window.ckeditorInstances = {};
                }
                window.ckeditorInstances[textareaId] = editor;

                // Remove required attribute to prevent validation conflicts
                if (textarea.prop('required')) {
                    textarea.removeAttr('required');
                    textarea.data('ckeditor-required', true);
                }

                // Apply styling
                const editorElement = textarea.next('.ck-editor');
                if (editorElement.length) {
                    editorElement.css({
                        'border': '1px solid #e4e6ef',
                        'border-radius': '0.475rem'
                    });
                }

                // Fast CKEditor initialized
            })
            .catch(error => {
                // Fast CKEditor initialization failed - fail silently
            });
    });
}

/**
 * Check if all CKEditor instances in a form are ready
 * @param {string} formSelector - CSS selector for the form
 * @returns {boolean} - True if all CKEditor instances are ready
 */
function areCKEditorsReady(formSelector) {
    const form = $(formSelector);
    let allReady = true;

    form.find('textarea').each(function() {
        const textareaId = $(this).attr('id');
        if (textareaId && (!window.ckeditorInstances || !window.ckeditorInstances[textareaId])) {
            allReady = false;
            return false; // Break the loop
        }
    });

    return allReady;
}

/**
 * Wait for CKEditor instances to be ready and then execute callback
 * @param {string} formSelector - CSS selector for the form
 * @param {Function} callback - Function to execute when ready
 * @param {number} maxWait - Maximum time to wait in milliseconds (default: 3000)
 */
function waitForCKEditorsReady(formSelector, callback, maxWait = 3000) {
    const startTime = Date.now();

    const checkReady = function() {
        if (areCKEditorsReady(formSelector)) {
            callback();
        } else if (Date.now() - startTime < maxWait) {
            setTimeout(checkReady, 100);
        } else {
            // CKEditor initialization timeout, proceeding anyway
            callback();
        }
    };

    checkReady();
}

/**
 * Initialize CKEditor immediately for a specific form
 * This ensures CKEditor is ready before form submission
 * @param {string} formSelector - CSS selector for the form
 * @param {Function} callback - Callback function when initialization is complete
 */
function initializeCKEditorForForm(formSelector, callback) {
    const form = $(formSelector);
    const textareas = form.find('textarea:not(.no-ckeditor):not([data-no-ckeditor])');

    if (textareas.length === 0) {
        if (callback) callback();
        return;
    }

    let initializedCount = 0;
    const totalTextareas = textareas.length;

    textareas.each(function() {
        const textarea = $(this);
        const textareaId = textarea.attr('id');

        if (!textareaId) {
            initializedCount++;
            if (initializedCount === totalTextareas && callback) callback();
            return;
        }

        // Skip if already initialized
        if (window.ckeditorInstances && window.ckeditorInstances[textareaId]) {
            initializedCount++;
            if (initializedCount === totalTextareas && callback) callback();
            return;
        }

        // Initialize CKEditor with optimized config
        const optimizedConfig = {
            toolbar: {
                items: [
                    'heading', '|',
                    'bold', 'italic', 'underline', '|',
                    'fontSize', 'fontColor', '|',
                    'numberedList', 'bulletedList', '|',
                    'link', 'insertTable', '|',
                    'undo', 'redo'
                ],
                shouldNotGroupWhenFull: true
            },
            height: 300,
            placeholder: textarea.attr('placeholder') || 'Enter your content here...',
            language: 'en'
        };

        ClassicEditor
            .create(this, optimizedConfig)
            .then(editor => {
                // Store editor instance
                if (!window.ckeditorInstances) {
                    window.ckeditorInstances = {};
                }
                window.ckeditorInstances[textareaId] = editor;

                // Handle required attribute
                if (textarea.prop('required')) {
                    textarea.removeAttr('required');
                    textarea.data('ckeditor-required', true);
                }

                // Apply styling
                const editorElement = textarea.next('.ck-editor');
                if (editorElement.length) {
                    editorElement.css({
                        'border': '1px solid #e4e6ef',
                        'border-radius': '0.475rem'
                    });
                }

                initializedCount++;
                // CKEditor initialized for form textarea

                if (initializedCount === totalTextareas && callback) {
                    callback();
                }
            })
            .catch(error => {
                // CKEditor initialization failed - fail silently
                initializedCount++;
                if (initializedCount === totalTextareas && callback) callback();
            });
    });
}

// Initialize CKEditor globally when the document is ready
$(document).ready(function() {
    initializeGlobalCKEditor();
});
