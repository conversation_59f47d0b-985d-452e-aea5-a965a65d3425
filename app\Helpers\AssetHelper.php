<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class AssetHelper
{
    /**
     * Get simple asset URL (simplified for performance)
     *
     * @param string $path
     * @return string
     */
    public static function asset($path)
    {
        // In production, use a simple version parameter
        if (app()->environment('production')) {
            return asset($path) . '?v=' . config('app.version', '1.0');
        }

        // In development, use file modification time for cache busting
        $fullPath = public_path($path);
        if (File::exists($fullPath)) {
            return asset($path) . '?v=' . filemtime($fullPath);
        }

        return asset($path);
    }

    /**
     * Get multiple CSS files (simplified - no combination)
     *
     * @param array $files
     * @return array
     */
    public static function getCssFiles(array $files): array
    {
        $urls = [];
        foreach ($files as $file) {
            $urls[] = self::asset($file);
        }
        return $urls;
    }

    /**
     * Get multiple JS files (simplified - no combination)
     *
     * @param array $files
     * @return array
     */
    public static function getJsFiles(array $files): array
    {
        $urls = [];
        foreach ($files as $file) {
            $urls[] = self::asset($file);
        }
        return $urls;
    }



    /**
     * Get critical CSS files (simplified)
     *
     * @return array
     */
    public static function getCriticalCssFiles(): array
    {
        return [
            self::asset('css/style.bundle.css'),
            self::asset('css/sidebar-active.css')
        ];
    }

    /**
     * Get preload tags for critical resources (simplified)
     *
     * @return string
     */
    public static function getPreloadTags(): string
    {
        $resources = [
            ['href' => self::asset('css/style.bundle.css'), 'as' => 'style'],
            ['href' => self::asset('js/scripts.bundle.js'), 'as' => 'script'],
        ];

        $tags = '';
        foreach ($resources as $resource) {
            $tags .= sprintf('<link rel="preload" href="%s" as="%s">' . "\n", $resource['href'], $resource['as']);
        }

        return $tags;
    }

    /**
     * Clear asset cache (simplified)
     *
     * @return void
     */
    public static function clearCache()
    {
        // Only clear specific cache keys to avoid performance issues
        Cache::forget('asset_version_*');
    }

    /**
     * Check if we're in production
     *
     * @return bool
     */
    public static function isProduction(): bool
    {
        return app()->environment('production');
    }
}
