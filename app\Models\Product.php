<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'barcode',
        'category_id',
        'brand_id',
        'supplier_id',
        'amount',
        'compare_price',
        'cost_price',
        'stock_quantity',
        'min_stock_level',
        'track_inventory',
        'stock_status',
        'weight',
        'length',
        'width',
        'height',
        'status',
        'is_featured',
        'is_digital',
        'requires_shipping',
        'is_taxable',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'featured_image',
        'gallery_images',
        'has_variants',
        'variant_options',
        'tags',
        'care_instructions',
        'warranty_info',
        'available_from',
        'available_until',
        'view_count',
        'purchase_count',
        'average_rating',
        'review_count',
        'published_at',
    ];

    protected $casts = [
        'gallery_images' => 'array',
        'variant_options' => 'array',
        'tags' => 'array',
        'amount' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'track_inventory' => 'boolean',
        'is_featured' => 'boolean',
        'is_digital' => 'boolean',
        'requires_shipping' => 'boolean',
        'is_taxable' => 'boolean',
        'has_variants' => 'boolean',
        'available_from' => 'date',
        'available_until' => 'date',
        'published_at' => 'datetime',
        'average_rating' => 'decimal:2',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
            if (empty($product->sku)) {
                $product->sku = 'PRD-' . strtoupper(Str::random(8));
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::deleting(function ($product) {
            // Delete product images when product is deleted
            $product->deleteImages();
        });
    }

    /**
     * Get the category that owns the product
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the first image URL
     */
    public function getFirstImageAttribute()
    {
        if ($this->images && is_array($this->images) && count($this->images) > 0) {
            return $this->images[0];
        }
        return null;
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get the first gallery image URL
     */
    public function getFirstGalleryImageAttribute()
    {
        if ($this->gallery_images && is_array($this->gallery_images) && count($this->gallery_images) > 0) {
            return $this->gallery_images[0];
        }
        return $this->featured_image;
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for featured products
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for products with stock
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    /**
     * Get the featured image URL with fallback
     */
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return asset('uploads/products/' . $this->featured_image);
        }

        if ($this->gallery_images && is_array($this->gallery_images) && count($this->gallery_images) > 0) {
            return asset('uploads/products/' . $this->gallery_images[0]);
        }

        return asset('images/no-image.png'); // Default placeholder
    }

    /**
     * Get all gallery image URLs
     */
    public function getGalleryImageUrlsAttribute()
    {
        if (!$this->gallery_images || !is_array($this->gallery_images)) {
            return [];
        }

        return array_map(function($image) {
            return asset('uploads/products/' . $image);
        }, $this->gallery_images);
    }

    /**
     * Delete all product images from storage
     */
    public function deleteImages()
    {
        $imagesToDelete = [];

        // Add featured image
        if ($this->featured_image) {
            $imagesToDelete[] = $this->featured_image;
        }

        // Add gallery images
        if ($this->gallery_images && is_array($this->gallery_images)) {
            $imagesToDelete = array_merge($imagesToDelete, $this->gallery_images);
        }

        // Delete files from storage
        foreach ($imagesToDelete as $image) {
            $imagePath = public_path('uploads/products/' . $image);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
    }

    /**
     * Delete specific image from gallery
     */
    public function deleteGalleryImage($imageToDelete)
    {
        if ($this->gallery_images && is_array($this->gallery_images)) {
            $updatedGallery = array_filter($this->gallery_images, function($image) use ($imageToDelete) {
                return $image !== $imageToDelete;
            });

            $this->gallery_images = array_values($updatedGallery);
            $this->save();

            // Delete file from storage
            $imagePath = public_path('uploads/products/' . $imageToDelete);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
    }

    /**
     * Add image to gallery
     */
    public function addToGallery($imageName)
    {
        $gallery = $this->gallery_images ?: [];
        $gallery[] = $imageName;
        $this->gallery_images = $gallery;
        $this->save();
    }

    /**
     * Get stock status with color coding
     */
    public function getStockStatusAttribute()
    {
        if ($this->stock_quantity > 10) {
            return [
                'status' => 'in_stock',
                'label' => 'In Stock',
                'color' => 'success',
                'icon' => 'check-circle'
            ];
        } elseif ($this->stock_quantity > 0) {
            return [
                'status' => 'low_stock',
                'label' => 'Low Stock',
                'color' => 'warning',
                'icon' => 'exclamation-triangle'
            ];
        } else {
            return [
                'status' => 'out_of_stock',
                'label' => 'Out of Stock',
                'color' => 'danger',
                'icon' => 'times-circle'
            ];
        }
    }
}
