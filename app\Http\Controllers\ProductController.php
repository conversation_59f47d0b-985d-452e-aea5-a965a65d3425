<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Helpers\Helper;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (request()->ajax()) {
            // Simplified DataTables query without excessive monitoring
            $query = Product::with('category:id,category')
                ->select(['id', 'name', 'slug', 'sku', 'amount', 'category_id', 'status', 'stock_quantity', 'is_featured', 'created_at'])
                ->orderBy('id', 'desc'); // Use id instead of created_at for better performance

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('category_name', function($row) {
                    return $row->category?->category ?? 'No Category';
                })
                ->addColumn('status_badge', function($row) {
                    return $row->status === 'active'
                        ? '<span class="badge badge-success">Active</span>'
                        : '<span class="badge badge-secondary">Inactive</span>';
                })
                ->addColumn('featured_badge', function($row) {
                    return $row->is_featured
                        ? '<span class="badge badge-warning">Featured</span>'
                        : '<span class="badge badge-light">Regular</span>';
                })
                ->addColumn('formatted_amount', function($row) {
                    return '$' . number_format($row->amount, 2);
                })
                ->addColumn('stock_status', function($row) {
                    if ($row->stock_quantity > 10) {
                        return '<span class="badge badge-success">In Stock</span>';
                    } elseif ($row->stock_quantity > 0) {
                        return '<span class="badge badge-warning">Low Stock</span>';
                    }
                    return '<span class="badge badge-danger">Out of Stock</span>';
                })
                ->addColumn('actions', function($row) {
                    return Helper::getActionButtons($row->id, 'products', ['show', 'edit', 'duplicate', 'delete']);
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at->format('M d, Y');
                })
                ->filterColumn('name', function($query, $keyword) {
                    $query->where('name', 'LIKE', "%{$keyword}%");
                })
                ->filterColumn('sku', function($query, $keyword) {
                    $query->where('sku', 'LIKE', "%{$keyword}%");
                })
                ->rawColumns(['status_badge', 'featured_badge', 'stock_status', 'actions'])
                ->make(true);
        }

        // Optimized single query for all counts
        $viewData = Cache::remember('products_view_data', 600, function () {
            $counts = Product::selectRaw('
                COUNT(*) as total_products,
                SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active_products,
                SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured_products,
                SUM(CASE WHEN stock_quantity = 0 THEN 1 ELSE 0 END) as out_of_stock
            ')->first();

            return [
                'total_products' => $counts->total_products,
                'active_products' => $counts->active_products,
                'featured_products' => $counts->featured_products,
                'out_of_stock' => $counts->out_of_stock,
            ];
        });

        // Cache categories separately with longer duration
        $categories = Cache::remember('active_categories', 1800, function () {
            return ProductCategory::where('status', 'active')->pluck('category', 'id');
        });

        $viewData['categories'] = $categories;
        return view('Product.index', $viewData);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ProductCategory::where('status', 'active')->pluck('category', 'id');
        return view('Product.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Streamlined validation
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:products,slug',
                'description' => 'nullable|string',
                'short_description' => 'nullable|string|max:500',
                'amount' => 'required|numeric|min:0',
                'category_id' => 'nullable|exists:product_categories,id',
                'sku' => 'nullable|string|max:100|unique:products,sku',
                'status' => 'required|string|in:active,inactive',
                'weight' => 'nullable|numeric|min:0',
                'stock_quantity' => 'required|integer|min:0',
                'cost_price' => 'nullable|numeric|min:0',
                'track_inventory' => 'boolean',
                'is_featured' => 'boolean',
                'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            ]);

            // Generate slug if not provided
            if (empty($validated['slug'])) {
                $validated['slug'] = $this->generateUniqueSlug($validated['name']);
            }

            // Generate SKU if not provided
            if (empty($validated['sku'])) {
                $validated['sku'] = $this->generateUniqueSku();
            }

            // Handle image uploads
            if ($request->hasFile('featured_image')) {
                $validated['featured_image'] = $this->uploadImage($request->file('featured_image'));
            }

            if ($request->hasFile('gallery_images')) {
                $validated['gallery_images'] = array_map(
                    fn($image) => $this->uploadImage($image),
                    $request->file('gallery_images')
                );
            }

            $product = Product::create($validated);

            // Clear cache
            Cache::forget('products_view_data');
            Cache::forget('active_categories');

            return Helper::successResponse('Product created successfully', $product);

        } catch (ValidationException $e) {
            return Helper::errorResponse('Validation failed: ' . collect($e->errors())->flatten()->implode(', '), 422);
        } catch (Exception $e) {
            Log::error('Product store failed: ' . $e->getMessage());
            return Helper::errorResponse('Failed to create product. Please try again.', 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $product = Cache::remember("product_show_{$id}", 600, function () use ($id) {
            return Product::with('category:id,category')->findOrFail($id);
        });

        return view('Product.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $product = Cache::remember("product_{$id}", 600, function () use ($id) {
            return Product::findOrFail($id);
        });

        return Helper::successResponse('Product fetched', $product);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:products,slug,' . $id,
                'description' => 'nullable|string',
                'short_description' => 'nullable|string|max:500',
                'amount' => 'required|numeric|min:0',
                'category_id' => 'nullable|exists:product_categories,id',
                'sku' => 'nullable|string|max:100|unique:products,sku,' . $id,
                'status' => 'required|string|in:active,inactive',
                'weight' => 'nullable|numeric|min:0',
                'stock_quantity' => 'required|integer|min:0',
                'cost_price' => 'nullable|numeric|min:0',
                'track_inventory' => 'boolean',
                'is_featured' => 'boolean',
                'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
                'remove_featured_image' => 'nullable|boolean',
                'remove_gallery_images' => 'nullable|array',
            ]);

            $product = Product::findOrFail($id);

            // Handle image operations
            $this->handleImageOperations($request, $product, $validated);

            $product->update($validated);

            // Clear cache
            Cache::forget("product_{$id}");
            Cache::forget("product_show_{$id}");
            Cache::forget('products_view_data');

            return Helper::successResponse('Product updated successfully', $product);

        } catch (ValidationException $e) {
            return Helper::errorResponse('Validation failed: ' . collect($e->errors())->flatten()->implode(', '), 422);
        } catch (Exception $e) {
            Log::error('Product update failed: ' . $e->getMessage());
            return Helper::errorResponse('Failed to update product. Please try again.', 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $product = Product::findOrFail($id);

            // Delete associated images
            if ($product->featured_image) {
                $this->deleteImage($product->featured_image);
            }

            if ($product->gallery_images) {
                foreach ($product->gallery_images as $image) {
                    $this->deleteImage($image);
                }
            }

            $product->delete();

            // Clear cache
            Cache::forget("product_{$id}");
            Cache::forget("product_show_{$id}");
            Cache::forget('products_view_data');

            return Helper::successResponse('Product deleted successfully');
        } catch (Exception $e) {
            Log::error('Product delete failed: ' . $e->getMessage());
            return Helper::errorResponse('Failed to delete product. Please try again.', 500);
        }
    }

    /**
     * Duplicate a product
     */
    public function duplicate(string $id)
    {
        try {
            $originalProduct = Product::findOrFail($id);
            $productData = $originalProduct->toArray();

            // Remove unique fields and set new values
            unset($productData['id'], $productData['created_at'], $productData['updated_at'], $productData['deleted_at']);

            $productData['name'] = $productData['name'] . ' (Copy)';
            $productData['slug'] = $this->generateUniqueSlug($productData['name']);
            $productData['sku'] = $this->generateUniqueSku();
            $productData['featured_image'] = null;
            $productData['gallery_images'] = null;

            $duplicateProduct = Product::create($productData);

            Cache::forget('products_view_data');

            return Helper::successResponse('Product duplicated successfully', $duplicateProduct);
        } catch (Exception $e) {
            Log::error('Product duplication failed: ' . $e->getMessage());
            return Helper::errorResponse('Failed to duplicate product. Please try again.', 500);
        }
    }

    /**
     * Generate unique slug for product
     */
    private function generateUniqueSlug(string $name): string
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (Product::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate unique SKU for product
     */
    private function generateUniqueSku(): string
    {
        do {
            $sku = 'PRD-' . strtoupper(Str::random(8));
        } while (Product::where('sku', $sku)->exists());

        return $sku;
    }

    /**
     * Handle image operations for product update
     */
    private function handleImageOperations(Request $request, Product $product, array &$validated): void
    {
        // Handle featured image removal
        if ($request->boolean('remove_featured_image') && $product->featured_image) {
            $this->deleteImage($product->featured_image);
            $validated['featured_image'] = null;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            if ($product->featured_image) {
                $this->deleteImage($product->featured_image);
            }
            $validated['featured_image'] = $this->uploadImage($request->file('featured_image'));
        }

        // Handle gallery image removal
        if ($request->has('remove_gallery_images') && is_array($request->remove_gallery_images)) {
            $oldGalleryImages = $product->gallery_images ?: [];
            foreach ($request->remove_gallery_images as $imageToRemove) {
                if (in_array($imageToRemove, $oldGalleryImages)) {
                    $this->deleteImage($imageToRemove);
                    $oldGalleryImages = array_filter($oldGalleryImages, fn($img) => $img !== $imageToRemove);
                }
            }
            $validated['gallery_images'] = array_values($oldGalleryImages);
        }

        // Handle new gallery images upload
        if ($request->hasFile('gallery_images')) {
            $currentGallery = $validated['gallery_images'] ?? $product->gallery_images ?: [];
            foreach ($request->file('gallery_images') as $image) {
                $currentGallery[] = $this->uploadImage($image);
            }
            $validated['gallery_images'] = $currentGallery;
        }
    }

    /**
     * Upload image to products directory
     */
    private function uploadImage($image): string
    {
        if (!$image || !$image->isValid()) {
            throw new Exception('Invalid image file');
        }

        $filename = time() . '_' . Str::random(8) . '.' . $image->getClientOriginalExtension();
        $uploadPath = public_path('uploads/products');

        if (!file_exists($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        $image->move($uploadPath, $filename);
        return $filename;
    }

    /**
     * Delete image from products directory
     */
    private function deleteImage(?string $filename): void
    {
        if (!$filename) {
            return;
        }

        $imagePath = public_path('uploads/products/' . $filename);
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }
    }

    /**
     * Remove image from gallery
     */
    public function removeGalleryImage(Request $request, string $id)
    {
        try {
            $request->validate(['image' => 'required|string']);

            $product = Product::findOrFail($id);
            $imageToRemove = $request->input('image');

            // Remove from gallery array
            $galleryImages = $product->gallery_images ?: [];
            if (in_array($imageToRemove, $galleryImages)) {
                $this->deleteImage($imageToRemove);
                $galleryImages = array_filter($galleryImages, fn($img) => $img !== $imageToRemove);
                $product->update(['gallery_images' => array_values($galleryImages)]);
            }

            // Clear cache
            Cache::forget("product_{$id}");
            Cache::forget("product_show_{$id}");

            return Helper::successResponse('Image removed successfully');
        } catch (Exception $e) {
            Log::error('Gallery image removal failed: ' . $e->getMessage());
            return Helper::errorResponse('Failed to remove image. Please try again.', 500);
        }
    }
}
