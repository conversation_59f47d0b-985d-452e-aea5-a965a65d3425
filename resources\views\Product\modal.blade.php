<!-- Product Modal -->
<div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalLabel">Add Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="productForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="product_id" name="id">

                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">Slug</label>
                                <input type="text" class="form-control" id="slug" name="slug" placeholder="Auto-generated if empty">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sku" class="form-label">SKU</label>
                                <input type="text" class="form-control" id="sku" name="sku" placeholder="Auto-generated if empty">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">Category</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">Select Category</option>
                                    @foreach($categories as $id => $category)
                                        <option value="{{ $id }}">{{ $category }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description</label>
                        <textarea class="form-control" id="short_description" name="short_description" rows="2" maxlength="500"></textarea>
                        <div class="form-text">Maximum 500 characters</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                    </div>

                    <!-- Pricing & Inventory -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Selling Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="price" name="amount" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock_quantity" class="form-label">Stock Quantity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="weight" class="form-label">Weight (kg)</label>
                                <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dimensions" class="form-label">Dimensions</label>
                                <input type="text" class="form-control" id="dimensions" name="dimensions" placeholder="L x W x H">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="draft">Draft</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Images -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="featured_image" class="form-label">Featured Image</label>
                                <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                                <div class="form-text">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF, WebP</div>
                                <div id="featured_image_preview" class="mt-2" style="display: none;">
                                    <img id="featured_image_img" src="" alt="Featured Image" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                    <button type="button" class="btn btn-sm btn-danger ms-2" id="remove_featured_image">Remove</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="gallery_images" class="form-label">Gallery Images</label>
                                <input type="file" class="form-control" id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                <div class="form-text">Max size per image: 2MB. Multiple images allowed</div>
                                <div id="gallery_images_preview" class="mt-2 d-flex flex-wrap gap-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Options -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" value="1" checked>
                                <label class="form-check-label" for="track_inventory">
                                    Track Inventory
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                <label class="form-check-label" for="is_featured">
                                    Featured Product
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Product</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end
        $('#slug').val(slug);
    });

    // Character counter for short description
    $('#short_description').on('input', function() {
        var maxLength = 500;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;

        if (remaining < 0) {
            $(this).val($(this).val().substring(0, maxLength));
            remaining = 0;
        }

        $(this).next('.form-text').text('Maximum 500 characters (' + remaining + ' remaining)');
    });

    // Featured image preview
    $('#featured_image').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#featured_image_img').attr('src', e.target.result);
                $('#featured_image_preview').show();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove featured image
    $('#remove_featured_image').on('click', function() {
        $('#featured_image').val('');
        $('#featured_image_preview').hide();
        $('#featured_image_img').attr('src', '');
    });

    // Gallery images preview
    $('#gallery_images').on('change', function() {
        var files = this.files;
        var previewContainer = $('#gallery_images_preview');
        previewContainer.empty();

        if (files.length > 0) {
            Array.from(files).forEach(function(file, index) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    var imagePreview = $(`
                        <div class="position-relative">
                            <img src="${e.target.result}" alt="Gallery Image" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 remove-gallery-preview" data-index="${index}" style="padding: 2px 6px; font-size: 10px;">×</button>
                        </div>
                    `);
                    previewContainer.append(imagePreview);
                };
                reader.readAsDataURL(file);
            });
        }
    });

    // Remove gallery image preview
    $(document).on('click', '.remove-gallery-preview', function() {
        var index = $(this).data('index');
        var fileInput = document.getElementById('gallery_images');
        var files = Array.from(fileInput.files);

        // Remove the file from the array
        files.splice(index, 1);

        // Create new FileList
        var dt = new DataTransfer();
        files.forEach(file => dt.items.add(file));
        fileInput.files = dt.files;

        // Refresh preview
        $('#gallery_images').trigger('change');
    });

    // Reset form when modal is hidden
    $('#productModal').on('hidden.bs.modal', function() {
        $('#productForm')[0].reset();
        $('#featured_image_preview').hide();
        $('#gallery_images_preview').empty();
        $('#product_id').val('');
    });
});
</script>
