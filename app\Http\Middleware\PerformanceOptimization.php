<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PerformanceOptimization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only monitor in development
        $startTime = app()->environment('local') ? microtime(true) : null;

        $response = $next($request);

        // Only apply optimizations to HTML responses
        if ($this->shouldOptimize($response)) {
            $this->addCacheHeaders($response);
            $this->addSecurityHeaders($response);

            // Add performance headers only in development
            if ($startTime) {
                $this->addPerformanceHeaders($response, $startTime);
            }

            $this->compressResponse($response);
        }

        return $response;
    }

    /**
     * Check if response should be optimized
     */
    private function shouldOptimize(Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');
        return str_contains($contentType, 'text/html') || empty($contentType);
    }

    /**
     * Add cache headers for better performance
     */
    private function addCacheHeaders(Response $response): void
    {
        if (app()->environment('production')) {
            // Cache static assets for 1 year
            if ($this->isStaticAsset()) {
                $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
                $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
            } else {
                // Cache HTML pages for 5 minutes
                $response->headers->set('Cache-Control', 'public, max-age=300, must-revalidate');
            }
        } else {
            // No cache in development
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        }

        // Add ETag for better caching
        $etag = md5($response->getContent());
        $response->headers->set('ETag', '"' . $etag . '"');
    }

    /**
     * Add security headers
     */
    private function addSecurityHeaders(Response $response): void
    {
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        if (app()->environment('production')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }
    }

    /**
     * Add performance headers
     */
    private function addPerformanceHeaders(Response $response, float $startTime): void
    {
        // DNS prefetch for external domains
        $dnsPrefetch = [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdnjs.cloudflare.com',
            'https://cdn.datatables.net',
            'https://cdn.jsdelivr.net'
        ];

        $linkHeader = [];
        foreach ($dnsPrefetch as $domain) {
            $linkHeader[] = '<' . $domain . '>; rel=dns-prefetch';
        }

        if (!empty($linkHeader)) {
            $response->headers->set('Link', implode(', ', $linkHeader));
        }

        // Add simple performance timing
        $executionTime = (microtime(true) - $startTime) * 1000;
        $response->headers->set('X-Response-Time', round($executionTime, 2) . 'ms');

        // Add memory usage header for debugging
        if (app()->environment(['local', 'staging'])) {
            $response->headers->set('X-Memory-Usage', round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB');
            $response->headers->set('X-Memory-Limit', ini_get('memory_limit'));
        }
    }

    /**
     * Compress response content (simplified)
     */
    private function compressResponse(Response $response): void
    {
        // Let the web server handle compression for better performance
        // This method is kept for compatibility but does minimal work
        if (app()->environment('production')) {
            $response->headers->set('Vary', 'Accept-Encoding', false);
        }
    }

    /**
     * Check if current request is for a static asset
     */
    private function isStaticAsset(): bool
    {
        $path = request()->path();
        $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'woff', 'woff2', 'ttf', 'eot'];

        foreach ($staticExtensions as $ext) {
            if (str_ends_with($path, '.' . $ext)) {
                return true;
            }
        }

        return false;
    }


}
