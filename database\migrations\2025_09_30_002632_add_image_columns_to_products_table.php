<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add image columns
            $table->string('featured_image')->nullable()->after('meta_data');
            $table->json('gallery_images')->nullable()->after('featured_image');

            // Add additional product fields that might be missing
            $table->decimal('compare_price', 10, 2)->nullable()->after('cost_price');
            $table->string('barcode')->nullable()->after('sku');
            $table->unsignedBigInteger('brand_id')->nullable()->after('category_id');
            $table->unsignedBigInteger('supplier_id')->nullable()->after('brand_id');
            $table->integer('min_stock_level')->default(0)->after('stock_quantity');
            $table->string('stock_status')->default('in_stock')->after('min_stock_level');
            $table->decimal('length', 8, 2)->nullable()->after('weight');
            $table->decimal('width', 8, 2)->nullable()->after('length');
            $table->decimal('height', 8, 2)->nullable()->after('width');
            $table->boolean('is_digital')->default(false)->after('is_featured');
            $table->boolean('requires_shipping')->default(true)->after('is_digital');
            $table->boolean('is_taxable')->default(true)->after('requires_shipping');
            $table->string('meta_title')->nullable()->after('is_taxable');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');
            $table->string('og_title')->nullable()->after('meta_keywords');
            $table->text('og_description')->nullable()->after('og_title');
            $table->string('og_image')->nullable()->after('og_description');
            $table->boolean('has_variants')->default(false)->after('og_image');
            $table->json('variant_options')->nullable()->after('has_variants');
            $table->json('tags')->nullable()->after('variant_options');
            $table->text('care_instructions')->nullable()->after('tags');
            $table->text('warranty_info')->nullable()->after('care_instructions');
            $table->date('available_from')->nullable()->after('warranty_info');
            $table->date('available_until')->nullable()->after('available_from');
            $table->unsignedInteger('view_count')->default(0)->after('available_until');
            $table->unsignedInteger('purchase_count')->default(0)->after('view_count');
            $table->decimal('average_rating', 3, 2)->default(0)->after('purchase_count');
            $table->unsignedInteger('review_count')->default(0)->after('average_rating');
            $table->timestamp('published_at')->nullable()->after('review_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Remove the added columns
            $table->dropColumn([
                'featured_image',
                'gallery_images',
                'compare_price',
                'barcode',
                'brand_id',
                'supplier_id',
                'min_stock_level',
                'stock_status',
                'length',
                'width',
                'height',
                'is_digital',
                'requires_shipping',
                'is_taxable',
                'meta_title',
                'meta_description',
                'meta_keywords',
                'og_title',
                'og_description',
                'og_image',
                'has_variants',
                'variant_options',
                'tags',
                'care_instructions',
                'warranty_info',
                'available_from',
                'available_until',
                'view_count',
                'purchase_count',
                'average_rating',
                'review_count',
                'published_at'
            ]);
        });
    }
};
